generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum AdvertisementStatus {
  CREATED
  APPROVED
  REJECTED

  @@map("advertisement_status")
}

enum AdvertisementParam {
  AREA
  ROOMS
  FLOOR
  ADDITIONAL_RENT

  @@map("advertisement_params")
}

enum AdvertisementSource {
  OLX
  OTODOM

  @@map("advertisement_type")
}

enum UserState {
  CREATED
  READY

  @@map("user_state")
}

model User {
  id          Int     @id @default(autoincrement())
  telegram_id String  @unique
  location_id Int?    @map("location_id")
  username    String?
  description String?

  state      UserState @default(CREATED)
  state_data Json?

  // Relations
  user_advertisements UserAdvertisement[]
  location            Location?           @relation(fields: [location_id], references: [id])

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Indexes
  @@index([telegram_id])
  @@index([location_id])
  @@index([username])
  @@index([created_at])
  @@map("users")
}

model Location {
  id              Int      @id @default(autoincrement())
  title           String
  normalized_name String   @unique
  lat             Decimal? @db.Decimal(9, 6)
  lon             Decimal? @db.Decimal(9, 6)
  parent_id       Int?     @map("parent_id")

  // Self-referential relation
  parent   Location?  @relation("LocationToLocation", fields: [parent_id], references: [id])
  children Location[] @relation("LocationToLocation")

  // Relations
  advertisements Advertisement[]

  created_at        DateTime            @default(now())
  updated_at        DateTime            @updatedAt
  UserAdvertisement UserAdvertisement[]
  User              User[]

  // Indexes
  @@index([id])
  @@map("locations")
}

model Advertisement {
  id          Int                 @id @default(autoincrement())
  title       String
  price       Decimal?            @db.Decimal(10, 2)
  description String?
  params      Json?
  url         String?
  source      AdvertisementSource @default(OLX)
  external_id String?
  expired_at  DateTime?
  status      AdvertisementStatus @default(CREATED)

  // Foreign keys
  location_id Int @map("location_id")

  // Relations
  location          Location            @relation(fields: [location_id], references: [id])
  images            Image[]
  UserAdvertisement UserAdvertisement[]

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Indexes
  @@index([location_id])
  @@index([status])
  @@index([created_at])
  @@index([status, location_id]) // Composite index for common queries
  @@map("advertisements")
}

model Image {
  id               Int    @id @default(autoincrement())
  url              String
  advertisement_id Int    @map("advertisement_id")

  // Relations
  advertisement Advertisement @relation(fields: [advertisement_id], references: [id], onDelete: Cascade)

  // Indexes
  @@index([advertisement_id])
  @@map("images")
}

model UserAdvertisement {
  id               Int       @id @default(autoincrement())
  user_id          Int       @map("user_id")
  advertisement_id Int       @map("advertisement_id")
  coefficient      Float     
  sent_at          DateTime? @map("sent_at")
  location_id      Int?      @map("location_id")  // Changed from locationId to location_id with @map

  // Relations
  user          User          @relation(fields: [user_id], references: [id])
  advertisement Advertisement @relation(fields: [advertisement_id], references: [id])
  location      Location?     @relation(fields: [location_id], references: [id])  // Updated relation field

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Constraints
  @@unique([user_id, advertisement_id]) // Prevent duplicate sends
  // Indexes
  @@index([user_id])
  @@index([advertisement_id])
  @@index([coefficient])
  @@map("user_advertisements")
}