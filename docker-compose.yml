services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3005:3005"
      - "5560:5560"
    environment:
      DATABASE_URL: ${DATABASE_URL}
    volumes:
       - ./:/app
       - /app/node_modules
    networks:
      - findflat-network
    command: sh -c "npx prisma migrate deploy && npm run start:dev"
    
networks:
  findflat-network:
    external: true

volumes:
  postgres_data: