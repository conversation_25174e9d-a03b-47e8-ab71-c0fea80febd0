# Dependency directories
node_modules/
pnpm-lock.yaml
package-lock.json
yarn.lock

# Build outputs
/dist/
/build/
/out/

# Environment variables
.env
.env.*
!.env.example

# IDE and editors
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/launch.json
!.vscode/tasks.json
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
/coverage/
/.nyc_output
/test-results/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Prisma
/prisma/migrations/
prisma/*.db
prisma/*.db-journal
prisma/.env
.env*.local

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.docker/data/
docker-compose.override.yml

# Cache and temporary files
.temp/
.cache/
.npm/
.eslintcache

# VS Code local history files
.history/
.history/**/*

# Build output
dist/

# Node modules
node_modules/

# Coverage reports
coverage/

.history/
.history/**/*