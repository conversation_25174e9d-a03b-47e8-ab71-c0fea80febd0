import Together from "together-ai";
import { User, Advertisement } from '@prisma/client';
import { JsonObject } from "@prisma/client/runtime/library";
import fs from 'fs';
const config = require('config');

class TogetherServiceAIService {
  private static instance: TogetherServiceAIService;
  private static systemPrompt: Array<any>;
  public apiClient: Together;

  private constructor() {
    this.apiClient = new Together();
  }

  public static getInstance(): TogetherServiceAIService {
    if (!TogetherServiceAIService.instance) {
      TogetherServiceAIService.instance = new TogetherServiceAIService();
    }
    return TogetherServiceAIService.instance;
  }

  public async loadMainInstructions() {
    return fs.readFileSync('config/main_instructions.txt', 'utf8');
  }

  public async buildSystemPrompt(): Promise<Array<any>> {
    return [
        {
          role: "user",
          content: await this.loadMainInstructions()
        },
        {
          role: "assistant",
          content: "1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0"
        }
    ];
  }

  public async sendMessage(content: Array<any>) {
    const response = await this.apiClient.chat.completions.create({
      messages: content,
      model: "meta-llama/Llama-4-Scout-17B-16E-Instruct"
    });

    return response.choices[0]?.message?.content;
  }

  public sanitize(text: string): string {
    text = text.replace(/<main_instructions>/gi, '');
    text = text.replace(/<\/main_instructions>/gi, '');

    text = text.replace(/<user_description>/gi, '');
    text = text.replace(/<\/user_description>/gi, '');

    text = text.replace(/<advertisement>/gi, '');
    text = text.replace(/<\/advertisement>/gi, '');

    return text;
  }

  private prepareUserPrompt(user: User): string {
    let userPrompt = user.description || '';
    userPrompt = this.sanitize(userPrompt);

    return `<user_description>${userPrompt}</user_description>`;
  }

  private prepareAdvertisementPrompt(advertisement: Advertisement, index: number) {
    let advertisementPrompt = advertisement.description || '';
    advertisementPrompt = this.sanitize(advertisementPrompt);

    let title = advertisement.title || '';
    title = this.sanitize(title);

    let params = '';
    if(advertisement.params) {
      const paramsObj = advertisement.params as JsonObject;
      for (const key in paramsObj) {
        params += `<param name="${key}" value="${paramsObj[key]}" />`;
      }
    }

    return `
        <advertisement index="${index + 1}">
            <title>${title}</title>
            <price>${advertisement.price}</price>
            <description>${advertisementPrompt}</description>
            <params>
                ${params}
            </params>
        </advertisement>`;
  }

  public async analyze(user: User, advertisements: Array<Advertisement>) {
    if(!TogetherServiceAIService.systemPrompt) {
      TogetherServiceAIService.systemPrompt = await this.buildSystemPrompt();
    }

    const userPrompt = this.prepareUserPrompt(user);

    let processedAdvertisements = advertisements.map((advertisement, index) => {
      return this.prepareAdvertisementPrompt(advertisement, index);
    });

    const content = [
      ...TogetherServiceAIService.systemPrompt,
      {
        role: "user",
        content: userPrompt + processedAdvertisements.join('')
      }
    ];

    let rating = await this.sendMessage(content) || '';
    let ratingList = rating.split(',').map((x) => parseFloat(x)).filter((x) => !isNaN(x));

    return ratingList;
  }
}

export const togetherService = TogetherServiceAIService.getInstance();