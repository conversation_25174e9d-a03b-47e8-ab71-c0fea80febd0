import { prismaService } from './prisma/prisma.service';
import { togetherService } from './prisma/together-ai.service';
const config = require('config');

interface User {
  id: number;
  telegram_id: string;
  location_id: number;
  username: string;
  description: string;
  state: string;
  state_data: any;
  created_at: Date;
  updated_at: Date;
  last_sent_at: Date;
}

export async function fetchUsers(
  usersPerRun: number,
  showCoefficient: number,
  maxQueuedAds: number,
  maxSentAdsPerDay: number,
  maxProcessedAdsPerDay: number
): Promise<User[]> {
  return await prismaService.prisma.$queryRaw<User[]>`
    SELECT
      u.id,
      u.telegram_id,
      u.location_id,
      u.username,
      u.description,
      u.state,
      u.state_data,
      u.created_at,
      u.updated_at,
      COALESCE(
        (
          SELECT ua.sent_at
          FROM user_advertisements ua
          WHERE ua.user_id = u.id
          ORDER BY ua.created_at DESC
          LIMIT 1
        ),
        '1900-01-01'::timestamp
      ) as last_sent_at
    FROM users u
    WHERE u.state = 'READY'
    AND (
      -- Users with no advertisements
      NOT EXISTS (
        SELECT 1
        FROM user_advertisements ua
        WHERE ua.user_id = u.id
      )
      OR
      -- Users who have at least one sent advertisement in their last 5
      (
          (
            SELECT COUNT(*)
            FROM user_advertisements ua
            WHERE ua.user_id = u.id
            AND ua.sent_at IS NULL
            AND ua.coefficient > ${showCoefficient}
          ) < ${maxQueuedAds}
          AND
          (
            SELECT COUNT(*)
            FROM user_advertisements ua
            WHERE ua.user_id = u.id
            AND ua.coefficient > ${showCoefficient}
            AND ua.created_at > NOW() - INTERVAL '1 days'
          ) < ${maxSentAdsPerDay}
          AND (
            SELECT COUNT(*)
            FROM user_advertisements ua
            WHERE ua.user_id = u.id
            AND ua.created_at > NOW() - INTERVAL '1 days'
          ) < ${maxProcessedAdsPerDay}
        )
    )
    ORDER BY
      last_sent_at ASC,  -- Oldest sent_at first (users with no ads get 1900-01-01)
      u.id ASC           -- Secondary sort for consistent ordering
    LIMIT ${usersPerRun};
  `;
}

async function main() {
  try {

    const usersPerRun = config.get('usersPerRun');
    const showCoefficient = config.get('showCoefficient');
    const maxQueuedAds = config.get('maxQueuedAds');
    const maxSentAdsPerDay = config.get('maxSentAdsPerDay');
    const maxProcessedAdsPerDay = config.get('maxProcessedAdsPerDay');
    console.log('starting');

    const users = await fetchUsers(
      usersPerRun,
      showCoefficient,
      maxQueuedAds,
      maxSentAdsPerDay,
      maxProcessedAdsPerDay
    );

    for (const user of users) {
      const newAdvertisements = await prismaService.prisma.advertisement.findMany({
        where: {
          AND: [
            { location_id: user.location_id }, // Same location as user
            { status: 'APPROVED' }, // Status is APPROVED
            {
              // Not expired
              OR: [{ expired_at: { gt: new Date() } }],
            },
            {
              // Not already sent to user
              UserAdvertisement: {
                none: { user_id: user.id },
              },
            },
          ],
        },
        orderBy: {
          created_at: 'desc',
        },
        take: 10,
      });

      const response = await togetherService.analyze(user, newAdvertisements);

      for (let i = 0; i < newAdvertisements.length; i++) {
        let rate = response[i] || 0;

        rate = parseFloat(rate.toFixed(2));
        rate = Math.min(rate, 1);
        rate = Math.max(rate, 0);

        await prismaService.prisma.userAdvertisement.create({
          data: {
            user_id: user.id,
            advertisement_id: newAdvertisements[i].id,
            coefficient: rate
          }
        });
      }

      console.log('User with id' + user.id + ' processed');
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  } catch (error) {
    console.error('Error in main function:', error);
  } finally {
    await prismaService.disconnect();
  }
}

main().catch(e => {
  console.error('Fatal error:', e);
  process.exit(1);
});
