{"name": "findflat", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:reset": "docker compose exec api npx prisma migrate reset --force", "db:migrate": "docker compose exec api npx prisma migrate dev", "db:seed": "docker compose exec api npx ts-node prisma/seed.ts", "db:studio": "docker compose exec -it api npx prisma studio --hostname 0.0.0.0", "db:generate": "docker compose exec api npx prisma generate", "start:local": "docker compose up -d && sleep 5 && npm run db:migrate && npm run db:seed", "setup-hooks": "simple-git-hooks", "format:check": "prettier --check \"src/**/*.{ts,js,json,md}\"", "format:fix": "prettier --write \"src/**/*.{ts,js,json,md}\"", "docker:restart": "docker compose down && docker compose up -d"}, "dependencies": {"@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.13.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudscraper": "^4.6.0", "config": "^4.1.0", "he": "^1.2.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "together-ai": "^0.22.0"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/he": "^1.2.3", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prisma": "^6.13.0", "simple-git-hooks": "^2.13.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*.{js,ts}": ["prettier --write"], "*.{json,md}": ["prettier --write"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}