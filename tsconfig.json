{"compilerOptions": {"module": "<PERSON><PERSON><PERSON>", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/*": ["src/*"], "@prisma/*": ["prisma/*"]}, "resolveJsonModule": true, "esModuleInterop": true}, "include": ["src/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist", "test"]}